package com.keyway.application.koin.modules
import com.keyway.adapters.executor.BaseUseCaseExecutor
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.service.concession.SaveConcessionServiceV2
import com.keyway.core.service.listing.GetPropertiesListingService
import com.keyway.core.service.listing.GroupedListingService
import com.keyway.core.usecases.backgorund.tasks.DeleteInactiveListingDataUseCase
import com.keyway.core.usecases.backgorund.tasks.NotifyLastRentByPropertyUseCase
import com.keyway.core.usecases.concessions.GetConcessionTypesDistributionUseCase
import com.keyway.core.usecases.concessions.GetPropertiesConcessionsV2UseCase
import com.keyway.core.usecases.concessions.SaveConcessionsV2UseCase
import com.keyway.core.usecases.historical.GetHistoricalConcessionByIdsUseCase
import com.keyway.core.usecases.historical.GetHistoricalExposureUseCase
import com.keyway.core.usecases.historical.GetHistoricalRents
import com.keyway.core.usecases.listings.CalculateRentSuggestionUseCase
import com.keyway.core.usecases.listings.GetFutureAvailabilityUseCase
import com.keyway.core.usecases.listings.GetPropertiesLastListingUseCase
import com.keyway.core.usecases.listings.GetPropertiesListingsUseCase
import com.keyway.core.usecases.listings.GetPropertyLastListingUseCase
import com.keyway.core.usecases.listings.GetPropertyListingsUseCase
import com.keyway.core.usecases.listings.ListingsWithEffectiveRentMapper
import com.keyway.core.usecases.listings.SaveGroupedListingUseCase
import com.keyway.core.usecases.listings.SaveHistoricalGroupedListingUseCase
import com.keyway.core.usecases.metrics.ComputeAggregatedGeoMetricUseCase
import com.keyway.core.usecases.metrics.ComputeAggregatedMetricUseCase
import com.keyway.core.usecases.multifamily.SaveOrUpdateProperty
import com.keyway.core.usecases.multifamily.units.SaveOrUpdatePropertyUnits
import org.koin.core.module.Module
import org.koin.dsl.module

object UseCasesModule : KoinModule {
    override fun get(): Module =
        module(createdAtStart = true) {
            // ========================================
            // CORE SERVICES & EXECUTORS
            // ========================================
            single<UseCaseExecutor> { BaseUseCaseExecutor }

            // ========================================
            // LISTINGS USE CASES
            // ========================================
            // Services
            single { GroupedListingService(get()) }
            single { GetPropertiesListingService(get()) }

            // Use Cases
            single { SaveGroupedListingUseCase(get(), get(), get(), get()) }
            single { SaveHistoricalGroupedListingUseCase(get(), get(), get()) }
            single { ListingsWithEffectiveRentMapper(get()) }
            single { GetPropertyLastListingUseCase(get(), get()) }
            single { GetPropertyListingsUseCase(get(), get()) }
            single { GetPropertiesListingsUseCase(get(), get()) }
            single { GetPropertiesLastListingUseCase(get(), get()) }
            single { GetFutureAvailabilityUseCase(get()) }
            single { CalculateRentSuggestionUseCase(get(), get(), get(), get()) }

            // ========================================
            // CONCESSIONS USE CASES
            // ========================================
            single { SaveConcessionsV2UseCase(get(), get(), get(), get()) }
            single { SaveConcessionServiceV2() }
            single { GetConcessionTypesDistributionUseCase(get()) }
            single { GetPropertiesConcessionsV2UseCase(get()) }

            // ========================================
            // HISTORICAL USE CASES
            // ========================================
            single { GetHistoricalRents(get()) }
            single { GetHistoricalConcessionByIdsUseCase(get()) }
            single { GetHistoricalExposureUseCase(get(), get()) }

            // ========================================
            // METRICS USE CASES
            // ========================================
            single { ComputeAggregatedMetricUseCase(get()) }
            single { ComputeAggregatedGeoMetricUseCase(get()) }

            // ========================================
            // MULTIFAMILY/PROPERTY USE CASES
            // ========================================
            single { SaveOrUpdatePropertyUnits(get()) }
            single { SaveOrUpdateProperty() }

            // ========================================
            // BACKGROUND TASKS USE CASES
            // ========================================
            single { DeleteInactiveListingDataUseCase(get(), get(), get()) }
            single { NotifyLastRentByPropertyUseCase(get(), get()) }
        }
}
