package com.keyway.application.router.concession

import com.keyway.adapters.handlers.rest.historical.GetHistoricalConcessionByIds
import com.keyway.adapters.handlers.rest.historical.GetHistoricalRentsByIds
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils
import com.keyway.application.utils.router.ParamUtils.MSA_TAG
import com.keyway.application.utils.router.ParamUtils.ZIP_TAG
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getPropertyId
import com.keyway.application.utils.router.RequestUtils.getStringPathParameterOrFail
import com.keyway.application.utils.router.RequestUtils.getStringQueryParameter
import com.keyway.core.dto.HistoricalConcession
import com.keyway.core.dto.HistoricalConcessionSummary
import com.keyway.core.dto.MsaHistoricalConcessionSummary
import com.keyway.core.dto.ZipHistoricalConcessionSummary
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import io.github.smiley4.ktoropenapi.config.RequestConfig
import io.github.smiley4.ktoropenapi.config.RouteConfig
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.RoutingContext
import java.time.LocalDate

class HistoricalConcessionRouter(
    private val getHistoricalByIds: GetHistoricalConcessionByIds,
) : Router {
    companion object {
        const val TAG = "historical"
    }

    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                "/multifamily/{${ParamUtils.PROPERTY_ID_PARAM_NAME}}/historical-concession-summary",
                {
                    tags = listOf(TAG)
                    summary = "Historical Concession Summary"
                    historicalBaseDocumentation {
                        pathParameter<String>(ParamUtils.PROPERTY_ID_PARAM_NAME) {
                            required = true
                            description = ParamUtils.PROPERTY_ID_PARAM_NAME_DESC
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<HistoricalConcessionSummary>() }
                    }
                },
            ) {
                call.getPropertyId().let { propId ->
                    call.respond(
                        getHistorical(
                            setOf(call.getPropertyId()),
                            idType = IdType.PROPERTY,
                        ),
                    )
                }
            }

            get(
                "/zip-codes/{${ParamUtils.ZIP_CODE_PARAM}}/historical-concession-summary",
                {
                    tags = listOf(ZIP_TAG)
                    summary = "ZIP Historical Concession Summary"
                    historicalBaseDocumentation {
                        pathParameter<String>(ParamUtils.ZIP_CODE_PARAM) {
                            required = true
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<ZipHistoricalConcessionSummary>() }
                    }
                },
            ) {
                call.getStringPathParameterOrFail(ParamUtils.ZIP_CODE_PARAM).let { zipCode ->
                    call.respond(
                        getHistorical(
                            setOf(zipCode),
                            idType = IdType.ZIP_CODE,
                        ),
                    )
                }
            }

            get(
                "/msa-codes/{${ParamUtils.MSA_CODE_PARAM}}/historical-concession-summary",
                {
                    tags = listOf(MSA_TAG)
                    summary = "MSA Historical Concession Summary"
                    historicalBaseDocumentation {
                        pathParameter<String>(ParamUtils.MSA_CODE_PARAM) {
                            required = true
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<MsaHistoricalConcessionSummary>() }
                    }
                },
            ) {
                call.getStringPathParameterOrFail(ParamUtils.MSA_CODE_PARAM).let { msaCode ->
                    call.respond(
                        getHistorical(
                            setOf(msaCode),
                            idType = IdType.MSA,
                        ),
                    )
                }
            }
        }
    }

    private suspend fun RoutingContext.getHistorical(
        ids: Set<String>,
        idType: IdType,
    ): HistoricalConcession =
        getHistoricalByIds(
            ids = ids,
            idType = idType,
            dateFrom =
                call.getLocalDateQueryParameter(ParamUtils.DATE_FROM_PARAM_NAME)
                    ?: LocalDate.now().minusDays(30),
            dateTo =
                call.getLocalDateQueryParameter(ParamUtils.DATE_TO_PARAM_NAME)
                    ?: LocalDate.now(),
            periodicity =
                call
                    .getStringQueryParameter(ParamUtils.PERIODICITY)
                    ?.let { HistoricalPeriodicity.valueOf(it.uppercase()) },
        )

    private fun RouteConfig.historicalBaseDocumentation(block: RequestConfig.() -> Unit = {}) =
        request {
            block()
            queryParameter<LocalDate>(ParamUtils.DATE_FROM_PARAM_NAME) {
                required = false
                description = ParamUtils.DATE_FROM_DESC
            }
            queryParameter<LocalDate>(ParamUtils.DATE_TO_PARAM_NAME) {
                required = false
                description = ParamUtils.DATE_TO_DESC
            }
            queryParameter<HistoricalPeriodicity>(ParamUtils.PERIODICITY) {
                required = false
                description =
                    """
                    Periodicity of the metric, default value will be calculated based on period.
                    
                    Max periods: 
                    - ${HistoricalPeriodicity.DAILY} ${GetHistoricalRentsByIds.Companion.MAX_DAILY_AMOUNT} days, 
                    - ${HistoricalPeriodicity.WEEKLY} ${GetHistoricalRentsByIds.Companion.MAX_WEEKLY_AMOUNT} weeks, 
                    - ${HistoricalPeriodicity.MONTHLY} ${GetHistoricalRentsByIds.Companion.MAX_MONTHLY_AMOUNT} months
                    """.trimIndent()
            }
        }
}
