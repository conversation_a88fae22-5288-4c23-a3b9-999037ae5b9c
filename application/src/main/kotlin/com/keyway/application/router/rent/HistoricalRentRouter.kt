package com.keyway.application.router.rent

import com.keyway.adapters.dtos.metrics.historical.HistoricalRentSummary
import com.keyway.adapters.dtos.metrics.historical.HistoricalSummary
import com.keyway.adapters.dtos.metrics.historical.MSAHistoricalRentSummary
import com.keyway.adapters.dtos.metrics.historical.ZipHistoricalRentSummary
import com.keyway.adapters.exceptions.RestNotFoundException
import com.keyway.adapters.handlers.rest.historical.GetHistoricalRentsByIds
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils.BATHROOMS_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.BEDROOMS_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.MSA_CODE_PARAM
import com.keyway.application.utils.router.ParamUtils.MSA_TAG
import com.keyway.application.utils.router.ParamUtils.PERIODICITY
import com.keyway.application.utils.router.ParamUtils.PROPERTY_ID_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.PROPERTY_ID_PARAM_NAME_DESC
import com.keyway.application.utils.router.ParamUtils.PROP_IDS_DESC
import com.keyway.application.utils.router.ParamUtils.PROP_IDS_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.RENT_TYPE
import com.keyway.application.utils.router.ParamUtils.UNIT_CONDITION_DESC
import com.keyway.application.utils.router.ParamUtils.UNIT_CONDITION_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.ZIP_CODE_PARAM
import com.keyway.application.utils.router.ParamUtils.ZIP_TAG
import com.keyway.application.utils.router.ParamUtils.historicalBaseDocumentation
import com.keyway.application.utils.router.RequestUtils.getBigDecimalQueryParameter
import com.keyway.application.utils.router.RequestUtils.getIntQueryParameter
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getPropertyId
import com.keyway.application.utils.router.RequestUtils.getPropertyIds
import com.keyway.application.utils.router.RequestUtils.getStringPathParameterOrFail
import com.keyway.application.utils.router.RequestUtils.getStringQueryParameter
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.entities.RentType
import com.keyway.core.entities.UnitCondition
import io.github.smiley4.ktoropenapi.config.RequestConfig
import io.github.smiley4.ktoropenapi.config.RouteConfig
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.RoutingContext
import java.time.LocalDate

class HistoricalRentRouter(
    private val getHistoricalRentsByIds: GetHistoricalRentsByIds,
) : Router {
    companion object {
        const val TAG = "historical"
    }

    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                "/multifamily/{$PROPERTY_ID_PARAM_NAME}/historical-rent-summary",
                {
                    tags = listOf(TAG)
                    summary = "Historical Rent Summary"
                    historicalDocumentation {
                        pathParameter<String>(PROPERTY_ID_PARAM_NAME) {
                            required = true
                            description = PROPERTY_ID_PARAM_NAME_DESC
                        }
                        queryParameter<UnitCondition>(UNIT_CONDITION_PARAM_NAME) {
                            required = false
                            description = UNIT_CONDITION_DESC
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<HistoricalRentSummary>() }
                    }
                },
            ) {
                call.getPropertyId().let { propId ->
                    call.respond(
                        getHistorical(
                            setOf(call.getPropertyId()),
                            idType = IdType.PROPERTY,
                        ).firstOrNull() ?: throw RestNotFoundException("No historic data found for $propId"),
                    )
                }
            }

            get(
                "/zip-codes/{$ZIP_CODE_PARAM}/historical-rent-summary",
                {
                    tags = listOf(ZIP_TAG)
                    summary = "ZIP Historical Rent Summary"
                    historicalDocumentation {
                        pathParameter<String>(ZIP_CODE_PARAM) {
                            required = true
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<ZipHistoricalRentSummary>() }
                    }
                },
            ) {
                call.getStringPathParameterOrFail(ZIP_CODE_PARAM).let { zipCode ->
                    call.respond(
                        getHistorical(
                            setOf(zipCode),
                            idType = IdType.ZIP_CODE,
                        ).firstOrNull() ?: throw RestNotFoundException("No historic data found for $zipCode"),
                    )
                }
            }

            get(
                "/msa-codes/{$MSA_CODE_PARAM}/historical-rent-summary",
                {
                    tags = listOf(MSA_TAG)
                    summary = "MSA Historical Rent Summary"
                    historicalDocumentation {
                        pathParameter<String>(MSA_CODE_PARAM) {
                            required = true
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<MSAHistoricalRentSummary>() }
                    }
                },
            ) {
                call.getStringPathParameterOrFail(MSA_CODE_PARAM).let { msaCode ->
                    call.respond(
                        getHistorical(
                            setOf(msaCode),
                            idType = IdType.MSA,
                        ).firstOrNull() ?: throw RestNotFoundException("No historic data found for $msaCode"),
                    )
                }
            }

            get(
                "/multifamily/historical-rent-summary",
                {
                    tags = listOf(TAG)
                    summary = "Historical Rent Summary"
                    historicalDocumentation {
                        queryParameter<List<String>>(PROP_IDS_PARAM_NAME) {
                            required = true
                            description = PROP_IDS_DESC
                        }
                        queryParameter<UnitCondition>(UNIT_CONDITION_PARAM_NAME) {
                            required = false
                            description = UNIT_CONDITION_DESC
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<List<HistoricalRentSummary>>() }
                    }
                },
            ) {
                call.respond(
                    getHistorical(
                        call.getPropertyIds(),
                        idType = IdType.PROPERTY,
                    ),
                )
            }
        }
    }

    private suspend fun RoutingContext.getHistorical(
        ids: Set<String>,
        idType: IdType,
    ): List<HistoricalSummary> =
        getHistoricalRentsByIds(
            ids = ids,
            idType = idType,
            dateFrom =
                call.getLocalDateQueryParameter(DATE_FROM_PARAM_NAME)
                    ?: LocalDate.now().minusDays(30),
            dateTo =
                call.getLocalDateQueryParameter(DATE_TO_PARAM_NAME)
                    ?: LocalDate.now(),
            rentType =
                call
                    .getStringQueryParameter(RENT_TYPE)
                    ?.let { RentType.valueOf(it.uppercase()) }
                    ?: RentType.ASKING,
            periodicity =
                call
                    .getStringQueryParameter(PERIODICITY)
                    ?.let { HistoricalPeriodicity.valueOf(it.uppercase()) },
            bedrooms = call.getIntQueryParameter(BEDROOMS_PARAM_NAME),
            bathrooms = call.getBigDecimalQueryParameter(BATHROOMS_PARAM_NAME),
            unitCondition = call.getStringQueryParameter(UNIT_CONDITION_PARAM_NAME)?.let { UnitCondition.valueOf(it.uppercase()) },
        )

    private fun RouteConfig.historicalDocumentation(block: RequestConfig.() -> Unit = {}) =
        historicalBaseDocumentation {
            block()
            queryParameter<RentType>(RENT_TYPE) {
                required = false
                description = "default: ${RentType.ASKING}"
            }
            queryParameter<Int>(BEDROOMS_PARAM_NAME) {
                required = false
            }
            queryParameter<Int>(BATHROOMS_PARAM_NAME) {
                required = false
            }
        }
}
