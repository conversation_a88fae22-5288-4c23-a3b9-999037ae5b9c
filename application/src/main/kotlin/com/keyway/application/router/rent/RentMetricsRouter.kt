package com.keyway.application.router.rent

import com.keyway.adapters.dtos.metrics.property.BedroomRentSummary
import com.keyway.adapters.dtos.metrics.property.FloorPlanRentSummary
import com.keyway.adapters.dtos.metrics.property.PropertyRentSummary
import com.keyway.adapters.dtos.metrics.property.UnitMixRentSummary
import com.keyway.adapters.dtos.metrics.property.UnitsRentSummary
import com.keyway.adapters.handlers.rest.metrics.GetPropertyMetricsHandler
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.UNIT_CONDITION_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.propIdsBaseParameterDoc
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getPropertyIds
import com.keyway.application.utils.router.RequestUtils.getStringQueryParameter
import com.keyway.core.entities.UnitCondition
import com.keyway.core.entities.metric.MetricType
import com.keyway.kommons.mapper.JsonMapper
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.RoutingContext
import org.slf4j.LoggerFactory

class RentMetricsRouter(
    private val getPropertyMetricsHandler: GetPropertyMetricsHandler,
) : Router {
    companion object {
        const val PATH = "/multifamily/metrics"
        const val TAG = "metrics"
    }

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                "$PATH/bedrooms",
                {
                    tags = listOf(TAG)
                    summary = "Rent metrics grouped by Bedrooms"
                    propIdsBaseParameterDoc()
                    response {
                        HttpStatusCode.OK to { body<List<BedroomRentSummary>>() }
                    }
                },
            ) {
                getMetrics(MetricType.BEDROOMS)
            }

            get(
                "$PATH/property",
                {
                    tags = listOf(TAG)
                    summary = "Rent metrics grouped by Property"
                    propIdsBaseParameterDoc()
                    response {
                        HttpStatusCode.OK to { body<List<PropertyRentSummary>>() }
                    }
                },
            ) {
                getMetrics(MetricType.BY_ID)
            }

            get(
                "$PATH/unit-mix",
                {
                    tags = listOf(TAG)
                    summary = "Rent metrics grouped by Unit Mix"
                    propIdsBaseParameterDoc()
                    response {
                        HttpStatusCode.OK to { body<List<UnitMixRentSummary>>() }
                    }
                },
            ) {
                getMetrics(MetricType.UNIT_MIX)
            }

            get(
                "$PATH/floor-plan",
                {
                    tags = listOf(TAG)
                    summary = "Rent metrics grouped by Floorplan"
                    propIdsBaseParameterDoc()
                    response {
                        HttpStatusCode.OK to { body<List<FloorPlanRentSummary>>() }
                    }
                },
            ) {
                getMetrics(MetricType.FLOOR_PLAN)
            }

            get(
                "$PATH/units",
                {
                    tags = listOf(TAG)
                    summary = "Rent metrics grouped by Units"
                    propIdsBaseParameterDoc()
                    response {
                        HttpStatusCode.OK to { body<List<UnitsRentSummary>>() }
                    }
                },
            ) {
                call.request.headers
                    .let { headers ->
                        headers.entries().map { header ->
                            header.key to header.value.first().take(26)
                        }
                    }.let {
                        logger.info("HEADERS -> ${JsonMapper.encode(it.toMap())}")
                    }

                getMetrics(MetricType.UNITS)
            }
        }
    }

    private suspend fun RoutingContext.getMetrics(metricType: MetricType) {
        call.respond(
            getPropertyMetricsHandler(
                GetPropertyMetricsHandler.Input(
                    propertyIds = call.getPropertyIds(),
                    dateFrom = call.getLocalDateQueryParameter(DATE_FROM_PARAM_NAME),
                    dateTo = call.getLocalDateQueryParameter(DATE_TO_PARAM_NAME),
                    metricType = metricType,
                    unitCondition = call.getStringQueryParameter(UNIT_CONDITION_PARAM_NAME)?.let { UnitCondition.valueOf(it.uppercase()) },
                ),
            ),
        )
    }
}
