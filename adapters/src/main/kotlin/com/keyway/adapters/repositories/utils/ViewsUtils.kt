package com.keyway.adapters.repositories.utils

import com.keyway.adapters.repositories.model.SummarizedView
import com.keyway.core.entities.RentType
import com.keyway.core.utils.DateUtils.toStartOfMonth
import java.time.LocalDate

object ViewsUtils {
    const val RENT_LISTING_VIEW = "rent_listing_by"
    const val EFFECTIVE_RENT_VIEW = "effective_rent_by"

    fun generateSummarizedView(
        rentType: RentType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): List<SummarizedView> =
        dateFrom.toStartOfMonth().let { start ->
            generateSequence(start) { it.plusMonths(1) }
                .takeWhile { it <= dateTo }
                .map { date -> SummarizedView(rentType, date) }
                .toList()
        }
}
