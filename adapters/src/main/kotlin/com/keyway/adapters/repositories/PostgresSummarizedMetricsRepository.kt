package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getBigDecimal
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getInt
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getOptionalInt
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getString
import com.keyway.adapters.repositories.utils.ViewsUtils.generateSummarizedView
import com.keyway.core.dto.GeoAskingMetricDto
import com.keyway.core.dto.GeoEffectiveMetricDto
import com.keyway.core.dto.GeoMetric
import com.keyway.core.dto.SummarizedMetricDto
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
import com.keyway.core.entities.RentType
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.ports.repositories.SummarizedMetricsRepository
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import java.math.BigDecimal

class PostgresSummarizedMetricsRepository(
    private val sqlClient: SqlClient,
) : SummarizedMetricsRepository {
    override suspend fun aggregateMetrics(metricsFiltersQuery: MetricsFiltersQuery): List<SummarizedMetricDto> =
        withContext(Dispatchers.IO) {
            val askingDeferred = async { getAskingMetrics(metricsFiltersQuery) }
            val effectiveDeferred = async { getEffectiveMetrics(metricsFiltersQuery) }

            val askingResult = askingDeferred.await()
            val effectiveResult = effectiveDeferred.await()

            val effectiveMap = effectiveResult.associateBy { it.code }
            askingResult.map { ask ->
                SummarizedMetricDto(ask, effectiveMap[ask.code])
            }
        }

    private fun getAskingMetrics(metricsFiltersQuery: MetricsFiltersQuery): List<GeoAskingMetricDto> {
        val mainResults =
            sqlClient.getAll(
                query = buildAskingMetricsQuery(metricsFiltersQuery),
                params = emptyList(),
            ) { result -> result }

        return mainResults.map { result ->
            buildGeoAskingDto(result, metricsFiltersQuery.idType)
        }
    }

    private fun buildAskingMetricsQuery(metricsFiltersQuery: MetricsFiltersQuery): String =
        """
         WITH filtered_rent_listings as (
        ${generateSummarizedView(rentType = RentType.ASKING, metricsFiltersQuery.dateFrom, metricsFiltersQuery.dateTo)
            .joinToString(" UNION ALL ") {
                "SELECT * from ${it.viewName} " +
                    it.getCondition(
                        metricsFiltersQuery.idType,
                        metricsFiltersQuery.ids.first(),
                        metricsFiltersQuery.dateFrom,
                        metricsFiltersQuery.dateTo,
                    )
            }}
            )
            SELECT 
                    ${metricsFiltersQuery.idType.getSqlColumn()} as id,
                    ${ "bedrooms,".takeIf { metricsFiltersQuery.type == MetricType.BEDROOMS } ?: "" }
                    count (distinct property_id) as total_properties,
                    MIN(min_rent) AS rent_min,
                    MAX(max_rent) AS rent_max,
                    ROUND(SUM(rent_sum) / SUM(total_records), 2) AS rent,
                    MIN(min_sft) AS unit_square_footage_min,
                    MAX(max_sft) AS unit_square_footage_max,
                    ROUND(SUM(sft_sum) / SUM(total_records), 2) AS unit_square_footage,
                    SUM(total_records)::BIGINT AS total_records,
                    ROUND(SUM(day_listing_since_publish) / SUM(total_records), 2) as average_listing_days
                FROM filtered_rent_listings
                GROUP BY ${metricsFiltersQuery.idType.getSqlColumn()}
                ${" , bedrooms".takeIf { metricsFiltersQuery.type == MetricType.BEDROOMS } ?: ""}
        """.trimIndent()

    private fun buildEffectiveMetricsQuery(metricsFiltersQuery: MetricsFiltersQuery): String =
        """
                  WITH filtered_rent_listings as (
        ${
            generateSummarizedView(rentType = RentType.EFFECTIVE, metricsFiltersQuery.dateFrom, metricsFiltersQuery.dateTo)
                .joinToString(" UNION ALL ") {
                    "SELECT * from ${it.viewName} " +
                        it.getCondition(
                            metricsFiltersQuery.idType,
                            metricsFiltersQuery.ids.first(),
                            metricsFiltersQuery.dateFrom,
                            metricsFiltersQuery.dateTo,
                        )
                }
        }
                 )
                 SELECT   
                         ${metricsFiltersQuery.idType.getSqlColumn()} as id,
                         ${"bedrooms,".takeIf { metricsFiltersQuery.type == MetricType.BEDROOMS } ?: ""}
                         MIN(min_rent) AS rent_min,
                         MAX(max_rent) AS rent_max,
                         ROUND(SUM(rent_sum) / SUM(total_records), 2) AS rent
                         FROM filtered_rent_listings
                         GROUP BY ${metricsFiltersQuery.idType.getSqlColumn()}
                      ${", bedrooms".takeIf { metricsFiltersQuery.type == MetricType.BEDROOMS } ?: ""}
        """.trimIndent()

    private fun getEffectiveMetrics(metricsFiltersQuery: MetricsFiltersQuery): List<GeoEffectiveMetricDto> =
        sqlClient.getAll(
            query = buildEffectiveMetricsQuery(metricsFiltersQuery),
            params = emptyList(),
        ) { result ->
            buildGeoEffectiveDto(result, metricsFiltersQuery.idType)
        }

    private fun buildGeoEffectiveDto(
        result: Map<String, Any>,
        idType: IdType,
    ): GeoEffectiveMetricDto =
        GeoEffectiveMetricDto(
            id = result.getString("id"),
            idType = idType,
            bedrooms = result.getOptionalInt("bedrooms"),
            effectiveRent = buildGeoMetric(result, "rent"),
        )

    private fun buildGeoAskingDto(
        result: Map<String, Any>,
        filteredColumn: IdType,
    ): GeoAskingMetricDto =
        GeoAskingMetricDto(
            id = result.getString("id"),
            idType = filteredColumn,
            totalRecords = result.getInt("total_records"),
            averageListingsDays = result.getBigDecimal("average_listing_days") ?: BigDecimal.ZERO,
            askingRent = buildGeoMetric(result, "rent")!!,
            squareFootage = buildGeoMetric(result, "unit_square_footage"),
            bedrooms = result.getOptionalInt("bedrooms"),
            totalProperties = result.getInt("total_properties"),
        )

    private fun buildGeoMetric(
        result: Map<String, Any>,
        prefix: String,
    ): GeoMetric? {
        val min = result.getBigDecimal("${prefix}_min")
        val max = result.getBigDecimal("${prefix}_max")
        val average = result.getBigDecimal(prefix)

        return takeIf { min != null && max != null && average != null }?.let {
            GeoMetric(min = min!!, max = max!!, average = average!!)
        }
    }
}
