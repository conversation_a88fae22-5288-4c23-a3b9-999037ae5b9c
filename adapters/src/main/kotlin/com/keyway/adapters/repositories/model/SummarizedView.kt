package com.keyway.adapters.repositories.model

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.ViewsUtils.EFFECTIVE_RENT_VIEW
import com.keyway.adapters.repositories.utils.ViewsUtils.RENT_LISTING_VIEW
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.RentType
import com.keyway.core.utils.DateUtils.equalMonthYear
import java.time.LocalDate

data class SummarizedView(
    val rentType: RentType,
    val date: LocalDate,
) {
    val viewName =
        when (rentType) {
            RentType.ASKING -> RENT_LISTING_VIEW
            RentType.EFFECTIVE -> EFFECTIVE_RENT_VIEW
        }.let {
            """${it}_${date.year}_${date.monthValue.toString().padStart(2, '0')}"""
        }

    fun getCondition(
        idType: IdType,
        id: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        bedrooms: Int? = null,
    ): String =
        """ 
                WHERE ${idType.getSqlColumn()} = '$id'
                ${getDateOfRecordCondition(dateFrom, dateTo)} 
                ${bedrooms?.let { " AND bedrooms = $it " } ?: ""}
        """.trimMargin()

    fun getCondition(
        idType: IdType,
        ids: Set<String>,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        bedrooms: Int? = null,
    ): String =
        """ 
                WHERE ${idType.getSqlColumn()} IN (${ids.joinToString(",") { "'$it'" }})
                ${getDateOfRecordCondition(dateFrom, dateTo)} 
                ${bedrooms?.let { " AND bedrooms = $it " } ?: ""}
        """.trimMargin()

    private fun getDateOfRecordCondition(
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): String =
        when {
            date.equalMonthYear(dateFrom) && date.equalMonthYear(dateTo) -> "  AND date_of_record BETWEEN '$dateFrom' AND '$dateTo' "
            date.equalMonthYear(dateFrom) -> "  AND date_of_record >= '$dateFrom' "
            date.equalMonthYear(dateTo) -> "  AND date_of_record <= '$dateTo' "
            else -> ""
        }
}
