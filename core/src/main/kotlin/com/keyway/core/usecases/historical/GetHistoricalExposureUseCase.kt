package com.keyway.core.usecases.historical

import com.keyway.core.dto.HistoricalExposure
import com.keyway.core.dto.HistoricalExposureSummary
import com.keyway.core.dto.HistoricalExposureValue
import com.keyway.core.dto.MsaHistoricalExposureSummary
import com.keyway.core.dto.ZipHistoricalExposureSummary
import com.keyway.core.dto.historical.HistoricalInput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.RentType
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.core.ports.repositories.MultifamilyPropertyRepository
import com.keyway.core.usecases.UseCaseAsync
import com.keyway.core.utils.BigDecimalUtil.safeDiv
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import java.math.BigDecimal

class GetHistoricalExposureUseCase(
    private val historicalRentRepository: HistoricalRentRepository,
    private val multifamilyPropertyRepository: MultifamilyPropertyRepository,
) : UseCaseAsync<
        HistoricalInput,
        HistoricalExposure,
    > {
    override suspend fun execute(input: HistoricalInput): HistoricalExposure =
        withContext(Dispatchers.IO) {
            val askingDeferred =
                async {
                    historicalRentRepository.getHistoricalRentsByProperty(
                        ids = input.ids,
                        idType = input.idType,
                        dateFrom = input.dateFrom,
                        dateTo = input.dateTo,
                        periodicity = input.periodicity,
                        rentType = RentType.ASKING,
                    )
                }

            val asking = askingDeferred.await()
            val propertyUnits = multifamilyPropertyRepository.getByPropertyUnitsByIds(asking.map { it.id }.toSet())

            val values =
                asking.groupBy { it.dateFrom to it.dateTo }.map { (key, value) ->

                    val propertyExposure =
                        value.mapNotNull { propValue ->
                            propertyUnits[propValue.id]?.takeIf { it > 0 }?.let { propValue.totalRecords.toBigDecimal() / it.toBigDecimal() }
                        }

                    HistoricalExposureValue(
                        dateFrom = key.first,
                        dateTo = key.second,
                        avgExposureRate = safeDiv(propertyExposure.sumOf { it }, propertyExposure.size.toBigDecimal()) ?: BigDecimal.ZERO,
                    )
                }

            val avgRate = safeDiv(values.sumOf { it.avgExposureRate }, values.size.toBigDecimal()) ?: BigDecimal.ZERO

            when (input.idType) {
                IdType.PROPERTY ->
                    HistoricalExposureSummary(
                        propertyId = input.ids.first(),
                        avgExposureRate = avgRate,
                        values = values,
                    )
                IdType.ZIP_CODE ->
                    ZipHistoricalExposureSummary(
                        zipCode = input.ids.first(),
                        avgExposureRate = avgRate,
                        values = values,
                    )
                IdType.MSA ->
                    MsaHistoricalExposureSummary(
                        msaCode = input.ids.first(),
                        avgExposureRate = avgRate,
                        values = values,
                    )
            }
        }
}
