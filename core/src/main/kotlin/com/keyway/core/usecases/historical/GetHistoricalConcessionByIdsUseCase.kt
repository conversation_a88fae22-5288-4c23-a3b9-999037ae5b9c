package com.keyway.core.usecases.historical

import com.keyway.core.dto.HistoricalConcession
import com.keyway.core.dto.HistoricalConcessionSummary
import com.keyway.core.dto.HistoricalConcessionValue
import com.keyway.core.dto.MsaHistoricalConcessionSummary
import com.keyway.core.dto.ZipHistoricalConcessionSummary
import com.keyway.core.dto.historical.HistoricalInput
import com.keyway.core.dto.historical.HistoricalRentOutput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentType
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.core.usecases.UseCaseAsync
import com.keyway.core.utils.BigDecimalUtil.safeDiv
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import java.math.BigDecimal

class GetHistoricalConcessionByIdsUseCase(
    private val historicalRentRepository: HistoricalRentRepository,
) : UseCaseAsync<
        HistoricalInput,
        HistoricalConcession,
    > {
    override suspend fun execute(input: HistoricalInput): HistoricalConcession =
        withContext(Dispatchers.IO) {
            val askingDeferred =
                async {
                    historicalRentRepository.getHistoricalRentsByProperty(
                        ids = input.ids,
                        idType = input.idType,
                        dateFrom = input.dateFrom,
                        dateTo = input.dateTo,
                        periodicity = input.periodicity,
                        rentType = RentType.ASKING,
                    )
                }

            val effectiveDeferred =
                async {
                    historicalRentRepository.getHistoricalRentsByProperty(
                        ids = input.ids,
                        idType = input.idType,
                        dateFrom = input.dateFrom,
                        dateTo = input.dateTo,
                        periodicity = input.periodicity,
                        rentType = RentType.EFFECTIVE,
                    )
                }

            val (asking, effective) = awaitAll(askingDeferred, effectiveDeferred)

            val effectiveByProperty = effective.byProperty()

            val values =
                asking.groupBy { it.dateFrom to it.dateTo }.map { (key, value) ->
                    val rentValues =
                        value.map {
                            RentValue(
                                askingRent = Money.of(it.totalRent),
                                effectiveRent = Money.of(effectiveByProperty[it.getKey()]?.totalRent ?: it.totalRent),
                                totalRecords = it.totalRecords,
                            )
                        }

                    val totalWithConcession = rentValues.count { it.hasConcession }

                    HistoricalConcessionValue(
                        dateFrom = key.first,
                        dateTo = key.second,
                        totalProperties = rentValues.size,
                        totalPropertiesWithConcession = totalWithConcession,
                        avgConcessionValue = safeDiv(rentValues.sumOf { it.avgDifference.value }, totalWithConcession.toBigDecimal()) ?: BigDecimal.ZERO,
                        avgConcessionRate = safeDiv(rentValues.sumOf { it.percentage.value }, totalWithConcession.toBigDecimal()) ?: BigDecimal.ZERO,
                    )
                }

            val avgConcessionValue = safeDiv(values.sumOf { it.avgConcessionValue }, values.size.toBigDecimal()) ?: BigDecimal.ZERO
            val avgConcessionRate = safeDiv(values.sumOf { it.avgConcessionRate }, values.size.toBigDecimal()) ?: BigDecimal.ZERO

            when (input.idType) {
                IdType.PROPERTY ->
                    HistoricalConcessionSummary(
                        propertyId = input.ids.first(),
                        avgConcessionValue = avgConcessionValue,
                        avgConcessionRate = avgConcessionRate,
                        values = values,
                    )
                IdType.ZIP_CODE ->
                    ZipHistoricalConcessionSummary(
                        zipCode = input.ids.first(),
                        avgConcessionValue = avgConcessionValue,
                        avgConcessionRate = avgConcessionRate,
                        values = values,
                    )
                IdType.MSA ->
                    MsaHistoricalConcessionSummary(
                        msaCode = input.ids.first(),
                        avgConcessionValue = avgConcessionValue,
                        avgConcessionRate = avgConcessionRate,
                        values = values,
                    )
            }
        }

    data class RentValue(
        val askingRent: Money,
        val effectiveRent: Money,
        val totalRecords: Int,
    ) {
        val difference = askingRent - effectiveRent
        val avgDifference = difference / Money.of(totalRecords.toBigDecimal())
        val percentage = difference / askingRent
        val hasConcession = difference > Money.of(BigDecimal.ZERO)
    }

    private fun HistoricalRentOutput.getKey() = "${this.id}-${this.dateFrom}-${this.dateTo}"

    private fun List<HistoricalRentOutput>.byProperty() = this.associateBy { it.getKey() }
}
