package com.keyway.core.usecases.historical

import com.keyway.core.dto.historical.HistoricalRentDto
import com.keyway.core.dto.historical.HistoricalRentOutput
import com.keyway.core.dto.historical.HistoricalRentSummaryDto
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.entities.RentType
import com.keyway.core.entities.UnitCondition
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.core.usecases.UseCaseAsync
import com.keyway.core.utils.BigDecimalUtil.safeDiv
import com.keyway.core.utils.distributeEvenly
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate

class GetHistoricalRents(
    private val historicalRentRepository: HistoricalRentRepository,
) : UseCaseAsync<GetHistoricalRents.Input, List<HistoricalRentSummaryDto>> {
    data class Input(
        val ids: Set<String>,
        val idType: IdType,
        val dateFrom: LocalDate,
        val dateTo: LocalDate,
        val periodicity: HistoricalPeriodicity,
        val rentType: RentType,
        val bedrooms: Int?,
        val bathrooms: BigDecimal?,
        val unitCondition: UnitCondition?,
    )

    override suspend fun execute(input: Input): List<HistoricalRentSummaryDto> =
        input.ids
            .distributeEvenly()
            .let { partitionedIds ->
                withContext(Dispatchers.IO) {
                    val deferredResults =
                        partitionedIds.map { ids ->
                            async {
                                historicalRentRepository
                                    .getHistoricalRents(
                                        ids = ids.toSet(),
                                        idType = input.idType,
                                        dateFrom = input.dateFrom,
                                        dateTo = input.dateTo,
                                        rentType = input.rentType,
                                        periodicity = input.periodicity,
                                        bedrooms = input.bedrooms,
                                        bathrooms = input.bathrooms,
                                        unitCondition = input.unitCondition,
                                    )
                            }
                        }
                    deferredResults.awaitAll().flatten()
                }
            }.groupBy {
                it.id
            }.map { (id, values) ->
                buildSummary(values, input.rentType, id)
            }

    private fun buildSummary(
        rents: List<HistoricalRentOutput>,
        rentType: RentType,
        id: String,
    ): HistoricalRentSummaryDto {
        val historicalRents = rents.toDto()

        return HistoricalRentSummaryDto(
            id = id,
            rentType = rentType,
            rentChange = historicalRents.getRentChange(),
            historicalRents = historicalRents,
        )
    }

    private fun List<HistoricalRentOutput>.toDto(): List<HistoricalRentDto> =
        this
            .groupBy { it.dateFrom to it.dateTo }
            .mapValues { (key, value) ->

                val rentAverage = value.sumOf { it.totalRent }.divide(value.sumOf { it.totalRecords }.toBigDecimal(), 2, RoundingMode.HALF_UP)

                val totalRecordsWithSquareFootage = value.sumOf { it.totalRecordsWithSquareFootage }.toBigDecimal()
                val rentWithSquareFootageAverage = safeDiv(value.sumOf { it.totalRentWithSquareFootage ?: BigDecimal.ZERO }, totalRecordsWithSquareFootage)
                val squareFootageAverage = safeDiv(value.sumOf { it.totalSquareFootage ?: BigDecimal.ZERO }, totalRecordsWithSquareFootage)

                HistoricalRentDto(
                    dateFrom = key.first,
                    dateTo = key.second,
                    averageRent = rentAverage,
                    medianRent = value.mapNotNull { it.rentMedian }.takeUnless { it.isEmpty() }?.avg(),
                    averageRentPSF =
                        value
                            .mapNotNull { safeDiv(rentWithSquareFootageAverage, squareFootageAverage) }
                            .takeUnless { it.isEmpty() }
                            ?.avg(),
                    medianRentPSF =
                        value
                            .mapNotNull { safeDiv(it.rentMedian, squareFootageAverage) }
                            .takeUnless { it.isEmpty() }
                            ?.avg(),
                )
            }.values
            .toList()

    private fun List<HistoricalRentDto>.getRentChange(): BigDecimal =
        this.let { value ->
            val min = value.minBy { it.dateTo }
            val max = value.maxBy { it.dateTo }
            (max.averageRent - min.averageRent) / min.averageRent
        }

    private fun Collection<BigDecimal>.avg() =
        this
            .reduce(BigDecimal::add)
            .divide(BigDecimal(this.size), 2, RoundingMode.HALF_UP)
}
