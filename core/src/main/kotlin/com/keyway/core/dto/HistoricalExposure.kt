package com.keyway.core.dto

import java.math.BigDecimal
import java.time.LocalDate

data class HistoricalExposureSummary(
    val propertyId: String,
    override val avgExposureRate: BigDecimal,
    override val values: List<HistoricalExposureValue>,
) : HistoricalExposure

data class MsaHistoricalExposureSummary(
    val msaCode: String,
    override val avgExposureRate: BigDecimal,
    override val values: List<HistoricalExposureValue>,
) : HistoricalExposure

data class ZipHistoricalExposureSummary(
    val zipCode: String,
    override val avgExposureRate: BigDecimal,
    override val values: List<HistoricalExposureValue>,
) : HistoricalExposure

data class HistoricalExposureValue(
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val avgExposureRate: BigDecimal,
)

sealed interface HistoricalExposure {
    val avgExposureRate: BigDecimal
    val values: List<HistoricalExposureValue>
}
