package com.keyway.core.dto.historical

import java.math.BigDecimal
import java.time.LocalDate

data class HistoricalRentOutput(
    val id: String,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val rentMedian: BigDecimal?,
    val totalRecords: Int,
    val totalRent: BigDecimal,
    val totalSquareFootage: BigDecimal?,
    val totalRentWithSquareFootage: BigDecimal?,
    val totalRecordsWithSquareFootage: Int,
)

data class HistoricalSummarizedRentOutput(
    val id: String,
    val dateOfRecord: LocalDate,
    val rentMedian: BigDecimal?,
    val totalRecords: Int,
    val totalRent: BigDecimal,
    val totalSquareFootage: BigDecimal?,
    val totalRentWithSquareFootage: BigDecimal?,
    val totalRecordsWithSquareFootage: Int,
)
