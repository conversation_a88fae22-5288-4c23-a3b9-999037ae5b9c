package core.usecase

import com.keyway.core.dto.listings.input.CalculateRentSuggestionInput
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.property.PropertyUnit
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.PropertyUnitRepository
import com.keyway.core.ports.repositories.UnitSimilarityCalculatorConfigRepository
import com.keyway.core.service.listing.GetPropertiesListingService
import com.keyway.core.usecases.listings.CalculateRentSuggestionUseCase
import com.keyway.core.utils.DateUtils
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import utils.MockedEntityFactory
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID
import kotlin.test.assertEquals

class CalculateRentSuggestionUseCaseTest {
    private lateinit var propertyUnitRepository: PropertyUnitRepository
    private lateinit var getPropertiesListingService: GetPropertiesListingService
    private lateinit var effectiveRentRepository: EffectiveRentRepository
    private lateinit var calculateRentSuggestionUseCase: CalculateRentSuggestionUseCase
    private lateinit var unitSimilarityCalculatorConfigRepository: UnitSimilarityCalculatorConfigRepository

    private val basePropertyId = "USFL-1111"
    private val compProperty1 = "USFL-2222"
    private val compProperty2 = "USFL-3333"

    @BeforeEach
    fun init() {
        propertyUnitRepository = mockk<PropertyUnitRepository>()
        getPropertiesListingService = mockk<GetPropertiesListingService>()
        effectiveRentRepository = mockk<EffectiveRentRepository>()
        unitSimilarityCalculatorConfigRepository = mockk<UnitSimilarityCalculatorConfigRepository>()
        every { unitSimilarityCalculatorConfigRepository.getDefault() } returns MockedEntityFactory.buildUnitSimilarityCalculatorConfig()
        calculateRentSuggestionUseCase =
            CalculateRentSuggestionUseCase(
                propertyUnitRepository = propertyUnitRepository,
                getPropertiesListingService = getPropertiesListingService,
                effectiveRentRepository = effectiveRentRepository,
                unitSimilarityCalculatorConfigRepository = unitSimilarityCalculatorConfigRepository,
            )
    }

    @Test
    fun `it calculates the suggested rent as expected`() {
        val mockedUnits = mockedUnits()
        val compsUnits =
            mockedUnits.filter { unit ->
                unit.propertyId in setOf(compProperty1, compProperty2)
            }

        val rentListings =
            compsUnits.map { unit ->
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = unit.propertyId,
                    type = RentListingType.UNIT,
                    typeId = unit.unitId,
                    rent = Money.of(1091 * unit.bedrooms),
                    dateFrom = LocalDate.parse("2023-08-03"),
                    dateTo = LocalDate.parse("2023-08-04"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = unit.bedrooms,
                    bathroomsQuantity = unit.bathrooms,
                    floorPlan = unit.floorPlan,
                    availableIn = null,
                    rentDeposit = Money.of(600),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                )
            }

        val effectiveRents =
            rentListings.map {
                EffectiveRent.build(
                    id = UUID.randomUUID().toString(),
                    listing = it,
                    concessionIds = listOf(),
                    dateFrom = LocalDate.now(),
                    dateTo = LocalDate.now(),
                    rent = Money.of(500 * it.bedroomsQuantity),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = OffsetDateTime.now(),
                    updateAt = OffsetDateTime.now(),
                    isActive = true,
                )
            }

        every { propertyUnitRepository.findByProperties(setOf(basePropertyId, compProperty1, compProperty2)) } returns mockedUnits
        every { getPropertiesListingService.all(any()) } returns rentListings

        coEvery { effectiveRentRepository.getByListingIds(listingIds = rentListings.map { it.id }, dateFrom = null, dateTo = null) } returns effectiveRents

        val suggestedRent =
            calculateRentSuggestionUseCase.execute(
                CalculateRentSuggestionInput(
                    organizationId = null,
                    basePropertyId = basePropertyId,
                    propertyIds = setOf(compProperty1, compProperty2),
                    dateFrom = null,
                    dateTo = null,
                ),
            )

        val firstUnit = suggestedRent.units[0]
        val secondUnit = suggestedRent.units[1]

        assertEquals(suggestedRent.propertyId, basePropertyId)
        assertEquals(suggestedRent.units.size, 2)
        assertEquals(firstUnit.unitId, "100")

        /*
         * The first unit is the one with ID 100 (1bed 1bath 150sqft)
         * similar units are:
         * 1) unitId 1 (1bed 1bath 150sqft)
         *     -> asking rent: 1091
         *     -> effective rent: 500
         * 2) unitId 11 (1bed 1bath 100sqft)
         *     -> asking rent: 1091
         *     -> effective rent: 500
         * 3) unitId 2 (2bed 1.5bath 200sqft)
         *     -> asking rent: 2182
         *     -> effective rent: 1000
         * 4) unitId 22 (2bed 1.5bath 100sqft)
         *     -> asking rent: 2182
         *     -> effective rent: 1000
         *
         * avg sqft = (150 + 100 + 200 + 100) / 4 = 137.5
         * asking avg -> (1091 + 1091 + 2182 + 2182) / 4 = 1636.5
         * effective avg -> (500 + 500 + 1000 + 1000) / 4 = 750
         * source unit sqft = 150
         *
         * asking = (1636.5 / 137.5) * 150 = 1785.27
         * effective = (750 / 137.5) * 150 = 818.18
         * */
        assertEquals(firstUnit.similarUnitsComparisons.size, 4)
        assertEquals(firstUnit.askingRent, BigDecimal("1785.27"))
        assertEquals(firstUnit.effectiveRent, BigDecimal("818.18"))

        /*
         * The second unit is the one with ID 200 (2bed 2bath 300sqft)
         * similar units are:
         * 1) unitId 2 (2bed 1.5bath 200sqft)
         *     -> asking rent: 2182
         *     -> effective rent: 1000
         * 2) unitId 3 (3bed 2.5bath 300sqft)
         *     -> asking rent: 3273
         *     -> effective rent: 1500
         * 3) unitId 201 (2bed 2bath 300sqft)
         *     -> asking rent: 2182
         *     -> effective rent: 1000
         * 4) unitId 202 (2bed 2bath 200sqft)
         *     -> asking rent: 2182
         *     -> effective rent: 1000
         * 5) unitId 205 (3bed 3bath 300sqft)
         *     -> asking rent: 3273
         *     -> effective rent: 1500
         *
         * avg sqft = (200 + 300 + 300 + 200 + 300) / 5 = 260
         * asking avg -> (2182 + 3273 + 2182 + 2182 + 3273) / 5 = 2618.4
         * effective avg -> (1000 + 1500 + 1000 + 1000 + 1500) / 5 = 1200
         * source unit sqft = 300
         *
         * asking = (2618.4 / 260) * 300
         * effective = (1200 / 260) * 300
         *
         * */
        assertEquals(secondUnit.similarUnitsComparisons.size, 5)
        assertEquals(secondUnit.askingRent, BigDecimal("3021.23"))
        assertEquals(secondUnit.effectiveRent, BigDecimal("1384.62"))
    }

    @Test
    fun `if there are 5 units with same unit mix it should use only them`() {
        val mockedUnits =
            mockedUnits().filter { !(it.propertyId == basePropertyId && it.unitId == "100") }.plus(
                PropertyUnit(
                    unitId = "2002",
                    propertyId = compProperty2,
                    squareFootage = BigDecimal("300"),
                    bedrooms = 2,
                    bathrooms = BigDecimal(2),
                    floorPlan = "F2",
                    renovated = false,
                    renovationProbability = BigDecimal("0.11"),
                    amenities = setOf("Pool", "BBQ", "Parking"),
                ),
            )
        val compUnitsWith2bed2bath = mockedUnits.filter { it.propertyId !== basePropertyId && it.bedrooms == 2 && it.bathrooms == BigDecimal(2) }

        val rentListings =
            compUnitsWith2bed2bath.map { unit ->
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = unit.propertyId,
                    type = RentListingType.UNIT,
                    typeId = unit.unitId,
                    rent = Money.of(1091 * unit.bedrooms),
                    dateFrom = LocalDate.parse("2023-08-03"),
                    dateTo = LocalDate.parse("2023-08-04"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = unit.bedrooms,
                    bathroomsQuantity = unit.bathrooms,
                    floorPlan = unit.floorPlan,
                    availableIn = null,
                    rentDeposit = Money.of(600),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                )
            }

        val effectiveRents =
            rentListings.map {
                EffectiveRent.build(
                    id = UUID.randomUUID().toString(),
                    listing = it,
                    concessionIds = listOf(),
                    dateFrom = LocalDate.now(),
                    dateTo = LocalDate.now(),
                    rent = Money.of(500 * it.bedroomsQuantity),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = OffsetDateTime.now(),
                    updateAt = OffsetDateTime.now(),
                    isActive = true,
                )
            }

        every { propertyUnitRepository.findByProperties(setOf(basePropertyId, compProperty1, compProperty2)) } returns mockedUnits
        every { getPropertiesListingService.all(any()) } returns rentListings

        coEvery { effectiveRentRepository.getByListingIds(listingIds = rentListings.map { it.id }, dateFrom = null, dateTo = null) } returns effectiveRents

        val suggestedRent =
            calculateRentSuggestionUseCase.execute(
                CalculateRentSuggestionInput(
                    organizationId = null,
                    basePropertyId = basePropertyId,
                    propertyIds = setOf(compProperty1, compProperty2),
                    dateFrom = null,
                    dateTo = null,
                ),
            )

        /*
         * The first unit is the one with ID 200 (2bed 2bath 300sqft)
         * similar units are:
         * 1) unitId 201(2bed 2bath 300sqft)
         *     -> asking rent: 1091
         *     -> effective rent: 500
         * 2) unitId 202 (2bed 2bath 200sqft)
         *     -> asking rent: 1091
         *     -> effective rent: 500
         * 3) unitId 203 (2bed 2bath 200sqft)
         *     -> asking rent: 2182
         *     -> effective rent: 1000
         * 4) unitId 205 (2bed 2bath 300sqft)
         *     -> asking rent: 2182
         *     -> effective rent: 1000
         *
         * avg sqft = (300 + 200 + 200 + 300) / 4 = 250
         * asking avg -> (2182 + 2182 + 2182 + 2182) / 4 = 2182
         * effective avg -> (1000 + 1000 + 1000 + 1000) / 4 = 1000
         * source unit sqft = 150
         *
         * asking = (2182 / 250) * 300 = 2618.40
         * effective = (1000 / 250) * 300 = 1200
         * */
        val firstUnit = suggestedRent.units[0]

        assertEquals(suggestedRent.propertyId, basePropertyId)
        assertEquals(suggestedRent.units.size, 1)
        assertEquals(firstUnit.unitId, "200")

        assertEquals(firstUnit.similarUnitsComparisons.size, 4)
        assertEquals(firstUnit.askingRent, BigDecimal("2618.40"))
        assertEquals(firstUnit.effectiveRent, BigDecimal("1200.00"))
    }

    private fun mockedUnits() =
        listOf(
            PropertyUnit(
                unitId = "100",
                propertyId = basePropertyId,
                squareFootage = BigDecimal("150"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "F1",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            PropertyUnit(
                unitId = "200",
                propertyId = basePropertyId,
                squareFootage = BigDecimal("300"),
                bedrooms = 2,
                bathrooms = BigDecimal(2),
                floorPlan = "F2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            // EQUALS
            PropertyUnit(
                unitId = "1",
                propertyId = compProperty1,
                squareFootage = BigDecimal("150"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "F1",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            // LESS SQFT SAME UNIT MIX
            PropertyUnit(
                unitId = "11",
                propertyId = compProperty1,
                squareFootage = BigDecimal("100"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "F1",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool"),
            ),
            // DIFFERENT RENO STATE
            PropertyUnit(
                unitId = "12",
                propertyId = compProperty1,
                squareFootage = BigDecimal("100"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "F1",
                renovated = true,
                renovationProbability = BigDecimal("0.99"),
                amenities = setOf("Pool"),
            ),
            // BIGGER IN UNIT MIX AND SQFT
            PropertyUnit(
                unitId = "2",
                propertyId = compProperty1,
                squareFootage = BigDecimal("200"),
                bedrooms = 2,
                bathrooms = BigDecimal("1.5"),
                floorPlan = "F2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool"),
            ),
            // BIGGER IN UNIT MIX BUT EQUALS IN SQFT
            PropertyUnit(
                unitId = "22",
                propertyId = compProperty1,
                squareFootage = BigDecimal("100"),
                bedrooms = 2,
                bathrooms = BigDecimal("1.5"),
                floorPlan = "F2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool"),
            ),
            PropertyUnit(
                unitId = "3",
                propertyId = compProperty1,
                squareFootage = BigDecimal("300"),
                bedrooms = 3,
                bathrooms = BigDecimal("2.5"),
                floorPlan = "F3",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool"),
            ),
            PropertyUnit(
                unitId = "201",
                propertyId = compProperty2,
                squareFootage = BigDecimal("300"),
                bedrooms = 2,
                bathrooms = BigDecimal(2),
                floorPlan = "Fx2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            PropertyUnit(
                unitId = "202",
                propertyId = compProperty2,
                squareFootage = BigDecimal("200"),
                bedrooms = 2,
                bathrooms = BigDecimal(2),
                floorPlan = "Fx2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            PropertyUnit(
                unitId = "203",
                propertyId = compProperty2,
                squareFootage = BigDecimal("200"),
                bedrooms = 2,
                bathrooms = BigDecimal(2),
                floorPlan = "Fx2",
                renovated = true,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            PropertyUnit(
                unitId = "204",
                propertyId = compProperty2,
                squareFootage = BigDecimal("450"),
                bedrooms = 2,
                bathrooms = BigDecimal(2),
                floorPlan = "Fx2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            PropertyUnit(
                unitId = "205",
                propertyId = compProperty2,
                squareFootage = BigDecimal("300"),
                bedrooms = 3,
                bathrooms = BigDecimal(3),
                floorPlan = "Fx2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
        )
}
