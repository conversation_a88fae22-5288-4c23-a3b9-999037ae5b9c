package integration.handlers.rest

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.metrics.historical.MSAHistoricalRentSummary
import com.keyway.adapters.dtos.metrics.historical.ZipHistoricalRentSummary
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.RentType
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.utils.DateUtils
import com.keyway.kommons.db.transaction.datasource.TransactionalDataSource
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class GetGeoHistoricalRentSummaryTest : BaseApplicationTest() {
    private val listingsRepository: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()
    private val dataSource: TransactionalDataSource by inject()

    private val testZipCode = "33009"
    private val testMsaCode = "35620"
    private val testPropertyId = "USFL-014695"

    @BeforeEach
    fun setUp() {
        // Create test data similar to HistoricalRentRepositoryTest
        var date = LocalDate.now().minusYears(1)
        for (x in 0..400) {
            val listing =
                getRentListing(
                    rent = Money.of("100") + Money.of(x),
                    dateTo = date,
                    dateFrom = date,
                )

            listingsRepository.save(listing).let {
                effectiveRentRepository.save(
                    EffectiveRent.build(
                        id = UUID.randomUUID().toString(),
                        rent = listing.rent,
                        rentDeposit = listing.rentDeposit,
                        dateTo = listing.dateTo,
                        dateFrom = listing.dateFrom,
                        listing = listing,
                        concessionIds = emptyList(),
                        concessions = "",
                        createdAt = listing.createdAt,
                        updateAt = listing.updateAt,
                    ),
                )
            }
            date = date.plusDays(1)
        }

        // Refresh materialized views
        dataSource.connection.use { connection ->
            connection.createStatement().use { statement ->
                // Create monthly rent views
                statement.execute("call public.create_monthly_rent_views()")

                // Refresh  listing materialized views
                statement.execute("call public.refresh_materialized_views('%_by_%')")
            }
        }
    }

    @Test
    fun `should get ZIP code historical rent summary with default parameters`() {
        // Given
        val givenUrl = "${localUrl()}/zip-codes/$testZipCode/historical-rent-summary"

        // When
        val result =
            Unirest
                .get(givenUrl)
                .asObject(ZipHistoricalRentSummary::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        assertNotNull(result.body)

        val summary = result.body
        assertEquals(testZipCode, summary.zipCode)
        assertEquals(RentType.ASKING, summary.rentType)
        assertTrue(summary.historicalRents.isNotEmpty())
        assertNotNull(summary.rentChange)
    }

    @Test
    fun `should get MSA code historical rent summary with default parameters`() {
        // Given
        val givenUrl = "${localUrl()}/msa-codes/$testMsaCode/historical-rent-summary"

        // When
        val result =
            Unirest
                .get(givenUrl)
                .asObject(MSAHistoricalRentSummary::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        assertNotNull(result.body)

        val summary = result.body
        assertEquals(testMsaCode, summary.msaCode)
        assertEquals(RentType.ASKING, summary.rentType)
        assertTrue(summary.historicalRents.isNotEmpty())
        assertNotNull(summary.rentChange)
    }

    @Test
    fun `should get ZIP code historical rent summary with custom date range`() {
        // Given
        val givenUrl = "${localUrl()}/zip-codes/$testZipCode/historical-rent-summary"
        val dateFrom = LocalDate.now().minusDays(10)
        val dateTo = LocalDate.now()

        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to dateFrom,
                        DATE_TO_PARAM_NAME to dateTo,
                    ),
                ).asObject(ZipHistoricalRentSummary::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        assertNotNull(result.body)

        val summary = result.body
        assertEquals(testZipCode, summary.zipCode)
        assertTrue(summary.historicalRents.isNotEmpty())

        // Verify date range
        summary.historicalRents.forEach { rent ->
            assertTrue(rent.dateFrom >= dateFrom || rent.dateFrom.isEqual(dateFrom))
            assertTrue(rent.dateTo <= dateTo || rent.dateTo.isEqual(dateTo))
        }
    }

    @Test
    fun `should get MSA code historical rent summary with custom date range`() {
        // Given
        val givenUrl = "${localUrl()}/msa-codes/$testMsaCode/historical-rent-summary"
        val dateFrom = LocalDate.now().minusDays(10)
        val dateTo = LocalDate.now()

        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to dateFrom,
                        DATE_TO_PARAM_NAME to dateTo,
                    ),
                ).asObject(MSAHistoricalRentSummary::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        assertNotNull(result.body)

        val summary = result.body
        assertEquals(testMsaCode, summary.msaCode)
        assertTrue(summary.historicalRents.isNotEmpty())

        // Verify date range
        summary.historicalRents.forEach { rent ->
            assertTrue(rent.dateFrom >= dateFrom || rent.dateFrom.isEqual(dateFrom))
            assertTrue(rent.dateTo <= dateTo || rent.dateTo.isEqual(dateTo))
        }
    }

    private fun getRentListing(
        rent: Money = Money.of("1000"),
        dateFrom: LocalDate = LocalDate.now(),
        dateTo: LocalDate = LocalDate.now(),
        bedroomsQuantity: Int = 2,
        bathroomsQuantity: BigDecimal = BigDecimal("1"),
    ) = RentListing(
        id = UUID.randomUUID().toString(),
        propertyId = testPropertyId,
        type = RentListingType.UNIT,
        typeId = "9-305",
        rent = rent,
        dateFrom = dateFrom,
        dateTo = dateTo,
        recordSource = "apartments",
        zipCode = testZipCode,
        msaCode = testMsaCode,
        unitSquareFootage = BigDecimal("1119"),
        bedroomsQuantity = bedroomsQuantity,
        bathroomsQuantity = bathroomsQuantity,
        floorPlan = "A2",
        availableIn = null,
        rentDeposit = Money.of("600"),
        createdAt = DateUtils.now(),
        updateAt = DateUtils.now(),
    )
}
