package integration.handlers.consumers

import application.utils.base.BaseApplicationTest
import application.utils.stub.StubMessagePublisher
import com.keyway.adapters.dtos.conciliation.RentTaskDataMessage
import com.keyway.adapters.repositories.model.EffectiveRentDBModel
import com.keyway.application.configuration.model.Configuration
import com.keyway.application.configuration.model.findQueueConfiguration
import com.keyway.core.dto.query.listings.PropertiesListingsQuery
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.Money
import com.keyway.core.entities.property.PropertyLastSeenData
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.kommons.aws.config.SqsConfig
import com.keyway.kommons.db.SqlClient
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.kommons.sqs.buildSqsClient
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.koin.test.get
import org.koin.test.inject
import software.amazon.awssdk.services.s3.S3Client
import utils.DateHelper
import utils.MockedEntityFactory
import utils.SqsHelpers.buildLocalstackQueueUrl
import utils.SqsHelpers.cleanQueue
import utils.SqsHelpers.receiveMessages
import utils.SqsHelpers.sendOversizeMessage
import utils.SqsHelpers.start
import java.time.Instant
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals

class RentTasksHandlerTest : BaseApplicationTest() {
    companion object {
        @JvmStatic
        @BeforeAll
        fun startConsumers() {
            consumers.start()
        }

        @JvmStatic
        @BeforeAll
        fun initClock() = DateHelper.setClockUTC(Instant.parse("2024-07-27T00:00:00Z"))
    }

    @AfterEach
    fun tearDown() {
        cleanQueue(client, queueUrl)
    }

    private val config: Configuration by inject()
    private val client = buildSqsClient(config.awsConfig)
    private val s3Client: S3Client by inject()
    private val queueName = get<SqsConfig>().findQueueConfiguration("background_rent_tasks").queueName
    private val queueUrl = buildLocalstackQueueUrl(queueName)

    private val listingsRepository: ListingsRepository by inject()
    private val effectiveRepository: EffectiveRentRepository by inject()
    private val sqlClient by inject<SqlClient>()

    @Test
    fun `should delete all inactive listing data`() {
        DateHelper.setClockUTC(Instant.parse("2024-06-01T00:00:00Z"))
        val propertyId = "USFL-014695"
        val message =
            RentTaskDataMessage(
                limit = 5,
                requestType = "DAY",
                type = "DELETE_DATA",
            )

        val id1 = UUID.randomUUID().toString()
        val id2 = UUID.randomUUID().toString()

        val listings =
            listOf(
                MockedEntityFactory.buildRentListing(
                    id = id1,
                    propertyId = propertyId,
                    dateFrom = LocalDate.parse("2024-07-20"),
                    dateTo = LocalDate.parse("2024-07-20"),
                    isActive = false,
                ),
                MockedEntityFactory.buildRentListing(
                    id = id2,
                    propertyId = propertyId,
                    dateFrom = LocalDate.parse("2024-07-25"),
                    dateTo = LocalDate.parse("2024-07-25"),
                    isActive = false,
                ),
            )
        listingsRepository.save(listings)

        val effectiveRent =
            listOf(
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == id1 },
                    dateFrom = LocalDate.parse("2024-07-25"),
                    dateTo = LocalDate.parse("2024-07-25"),
                    isActive = false,
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == id2 },
                    dateFrom = LocalDate.parse("2024-07-20"),
                    dateTo = LocalDate.parse("2024-07-20"),
                    isActive = false,
                ),
            )
        effectiveRepository.save(effectiveRent)

        sendOversizeMessage(client, s3Client, queueUrl, message)

        val query =
            PropertiesListingsQuery(
                propertyIds = setOf(propertyId),
                dateFrom = LocalDate.parse("2024-07-12"),
                dateTo = LocalDate.parse("2024-07-25"),
                typeId = null,
                bedrooms = null,
                bathrooms = null,
                floorPlan = null,
            )
        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findInactiveListings(10)
                }
            }

        assertNotNull(result)
        assertTrue(result.isEmpty())

        val effectiveResult = getInactiveEffective(query)
        assertNotNull(effectiveResult)
        assertTrue(effectiveResult.isEmpty())
    }

    @Test
    fun `should delete inactive listing data with a limit lower than the quantity`() {
        DateHelper.setClockUTC(Instant.parse("2024-08-01T00:00:00Z"))
        val propertyId = "USFL-014695"
        val message =
            RentTaskDataMessage(
                limit = 5,
                requestType = "DAY",
                type = "DELETE_DATA",
            )

        val listingId1 = UUID.randomUUID().toString()
        val listingId2 = UUID.randomUUID().toString()

        val listings =
            listOf(
                MockedEntityFactory.buildRentListing(
                    id = listingId1,
                    propertyId = propertyId,
                    rent = Money.of("1723.00"),
                    dateFrom = LocalDate.parse("2024-07-12"),
                    dateTo = LocalDate.parse("2024-07-12"),
                    isActive = false,
                ),
                MockedEntityFactory.buildRentListing(
                    propertyId = propertyId,
                    rent = Money.of("1724.00"),
                    dateFrom = LocalDate.parse("2024-07-20"),
                    dateTo = LocalDate.parse("2024-07-20"),
                    isActive = false,
                ),
                MockedEntityFactory.buildRentListing(
                    propertyId = propertyId,
                    rent = Money.of("1725.00"),
                    dateFrom = LocalDate.parse("2024-07-25"),
                    dateTo = LocalDate.parse("2024-07-25"),
                    isActive = false,
                ),
                MockedEntityFactory.buildRentListing(
                    propertyId = propertyId,
                    rent = Money.of("1726.00"),
                    dateFrom = LocalDate.parse("2024-07-26"),
                    dateTo = LocalDate.parse("2024-07-26"),
                    isActive = false,
                ),
                MockedEntityFactory.buildRentListing(
                    propertyId = propertyId,
                    rent = Money.of("1727.00"),
                    dateFrom = LocalDate.parse("2024-07-27"),
                    dateTo = LocalDate.parse("2024-07-27"),
                    isActive = false,
                ),
                MockedEntityFactory.buildRentListing(
                    propertyId = propertyId,
                    rent = Money.of("1728.00"),
                    dateFrom = LocalDate.parse("2024-07-28"),
                    dateTo = LocalDate.parse("2024-07-28"),
                    isActive = false,
                ),
                MockedEntityFactory.buildRentListing(
                    id = listingId2,
                    propertyId = propertyId,
                    rent = Money.of("1723.00"),
                    dateFrom = LocalDate.parse("2024-07-10"),
                    dateTo = LocalDate.parse("2024-07-10"),
                    isActive = false,
                ),
            )

        listingsRepository.save(listings)

        val effectiveRent =
            listOf(
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == listingId1 },
                    dateFrom = LocalDate.parse("2024-07-12"),
                    dateTo = LocalDate.parse("2024-07-12"),
                    isActive = false,
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == listingId1 },
                    dateFrom = LocalDate.parse("2024-07-20"),
                    dateTo = LocalDate.parse("2024-07-20"),
                    isActive = false,
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == listingId1 },
                    dateFrom = LocalDate.parse("2024-07-25"),
                    dateTo = LocalDate.parse("2024-07-25"),
                    isActive = false,
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == listingId1 },
                    dateFrom = LocalDate.parse("2024-07-26"),
                    dateTo = LocalDate.parse("2024-07-26"),
                    isActive = false,
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == listingId1 },
                    dateFrom = LocalDate.parse("2024-07-27"),
                    dateTo = LocalDate.parse("2024-07-27"),
                    isActive = false,
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == listingId2 },
                    dateFrom = LocalDate.parse("2024-07-05"),
                    dateTo = LocalDate.parse("2024-07-07"),
                    isActive = false,
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == listingId2 },
                    dateFrom = LocalDate.parse("2024-07-10"),
                    dateTo = LocalDate.parse("2024-07-10"),
                    isActive = false,
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == listingId2 },
                    dateFrom = LocalDate.parse("2024-07-11"),
                    dateTo = LocalDate.parse("2024-07-11"),
                    isActive = false,
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == listingId2 },
                    dateFrom = LocalDate.parse("2024-07-12"),
                    dateTo = LocalDate.parse("2024-07-12"),
                    isActive = false,
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == listingId2 },
                    dateFrom = LocalDate.parse("2024-07-13"),
                    dateTo = LocalDate.parse("2024-07-13"),
                    isActive = false,
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == listingId2 },
                    dateFrom = LocalDate.parse("2024-07-14"),
                    dateTo = LocalDate.parse("2024-07-14"),
                    isActive = false,
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listings.first { it.id == listingId2 },
                    dateFrom = LocalDate.parse("2024-07-15"),
                    dateTo = LocalDate.parse("2024-07-15"),
                    isActive = false,
                ),
            )
        effectiveRepository.save(effectiveRent)

        val query =
            PropertiesListingsQuery(
                propertyIds = setOf(propertyId),
                dateFrom = LocalDate.parse("2024-07-01"),
                dateTo = LocalDate.parse("2024-07-31"),
                typeId = null,
                bedrooms = null,
                bathrooms = null,
                floorPlan = null,
            )
        sendOversizeMessage(client, s3Client, queueUrl, message)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findInactiveListings(10)
                }
            }

        assertNotNull(result)
        assertEquals(2, result.size)

        val effectiveResult = getInactiveEffective(query)
        assertNotNull(effectiveResult)
        assertEquals(2, effectiveResult.size)
    }

    @Test
    fun `should notify last listing`() {
        val propertyId = "USFL-014695"
        val date = LocalDate.now()
        val message =
            RentTaskDataMessage(
                limit = 5,
                requestType = "LAST_RENT_NOTIFICATION",
                type = "LAST_RENT_NOTIFICATION",
            )

        val listing =

            MockedEntityFactory.buildRentListing(
                id = UUID.randomUUID().toString(),
                propertyId = propertyId,
                dateFrom = LocalDate.parse("2024-07-20"),
                dateTo = date,
                isActive = false,
            )
        listingsRepository.save(listOf(listing))

        sendOversizeMessage(client, s3Client, queueUrl, message)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    (messagePublisher as StubMessagePublisher).getMessages()
                }
            }

        assertNotNull(result)
        assertTrue(result.isNotEmpty())
        assertEquals(result.first(), JsonMapper.encode(PropertyLastSeenData(propertyId, date)))
    }

    private fun getInactiveEffective(propertiesListingsQuery: PropertiesListingsQuery): List<EffectiveRent> =
        sqlClient
            .getAll(
                query =
                    """
                    SELECT *
                    FROM effective_rent
                    WHERE date_from <= ?
                    AND date_to >= ?
                    """.trimIndent(),
                params =
                    listOfNotNull(
                        propertiesListingsQuery.dateTo,
                        propertiesListingsQuery.dateFrom,
                    ),
                clazz = EffectiveRentDBModel::class.java,
            ).map {
                it.toEffectiveRent()
            }
}
