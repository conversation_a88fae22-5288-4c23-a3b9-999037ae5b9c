package adapters.handlers.rest

import adapters.handlers.rest.RentListingExtensions.toEffectiveRentsOutput
import com.keyway.adapters.dtos.listings.PropertiesListingDataInput
import com.keyway.adapters.dtos.listings.PropertiesListingsResponse
import com.keyway.adapters.dtos.listings.RentListingResponse
import com.keyway.adapters.exceptions.RestException
import com.keyway.adapters.executor.BaseUseCaseExecutor
import com.keyway.adapters.handlers.rest.listings.GetPropertiesListingsHandler
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.usecases.listings.GetPropertiesListingsUseCase
import com.keyway.core.utils.DateUtils
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import utils.DateHelper
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.util.UUID

class GetPropertiesListingsHandlerTest {
    @Test
    fun `should execute the use case successfully`() {
        DateHelper.setClockUTC(Instant.parse("2023-10-20T00:00:00Z"))
        val input =
            PropertiesListingDataInput(
                propertiesIds = setOf("USTX-027626,USTX-022222"),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = null,
                bathrooms = null,
                bedrooms = null,
            )

        val propertyOne =
            listOf(
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "141",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-08-03"),
                    dateTo = LocalDate.parse("2023-08-04"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = Money.of(600),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ).toEffectiveRentsOutput(),
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "236",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-09-29"),
                    dateTo = LocalDate.parse("2023-09-29"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ).toEffectiveRentsOutput(),
            )

        val propertyTwo =
            listOf(
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-022222",
                    type = RentListingType.UNIT,
                    typeId = "104",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-10-17"),
                    dateTo = LocalDate.parse("2023-10-17"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ).toEffectiveRentsOutput(),
            )

        val mockedResponse =
            mapOf(
                "USTX-027626" to propertyOne,
                "USTX-022222" to propertyTwo,
            )

        val expectedResponse =
            listOf(
                PropertiesListingsResponse(
                    propertyId = "USTX-027626",
                    listings =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "141",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-08-03"),
                                listingTo = LocalDate.parse("2023-08-04"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = BigDecimal("600.00"),
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "236",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-09-29"),
                                listingTo = LocalDate.parse("2023-09-29"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ),
                ),
                PropertiesListingsResponse(
                    propertyId = "USTX-022222",
                    listings =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "104",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-10-17"),
                                listingTo = LocalDate.parse("2023-10-17"),
                                availableOn = LocalDate.parse("2023-10-20"),
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = true,
                            ),
                        ),
                ),
            )

        val useCase = mockk<GetPropertiesListingsUseCase>()
        every { useCase.execute(any()) } returns mockedResponse

        val handler = GetPropertiesListingsHandler(BaseUseCaseExecutor, useCase)
        val result = handler.invoke(input)

        // Then
        assertNotNull(result)
        assertEquals(expectedResponse, result)
    }

    @Test
    fun `should execute the use case successfully only with one id`() {
        val input =
            PropertiesListingDataInput(
                propertiesIds = setOf("USTX-027626"),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = null,
                bathrooms = null,
                bedrooms = null,
            )

        val propertyOne =
            listOf(
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "141",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-08-03"),
                    dateTo = LocalDate.parse("2023-08-04"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = Money.of(600),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ).toEffectiveRentsOutput(),
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "236",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-09-29"),
                    dateTo = LocalDate.parse("2023-09-29"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ).toEffectiveRentsOutput(),
            )

        val mockedResponse =
            mapOf(
                "USTX-027626" to propertyOne,
            )

        val expectedResponse =
            listOf(
                PropertiesListingsResponse(
                    propertyId = "USTX-027626",
                    listings =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "141",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-08-03"),
                                listingTo = LocalDate.parse("2023-08-04"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = BigDecimal("600.00"),
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "236",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-09-29"),
                                listingTo = LocalDate.parse("2023-09-29"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ),
                ),
            )

        val useCase = mockk<GetPropertiesListingsUseCase>()
        every { useCase.execute(any()) } returns mockedResponse

        val handler = GetPropertiesListingsHandler(BaseUseCaseExecutor, useCase)
        val result = handler.invoke(input)

        // Then
        assertNotNull(result)
        assertEquals(expectedResponse, result)
    }

    @Test
    fun `should convert exception into a error response`() {
        val input =
            PropertiesListingDataInput(
                propertiesIds = setOf("USTX-027626"),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = null,
                bathrooms = null,
                bedrooms = null,
            )
        val useCase = mockk<GetPropertiesListingsUseCase>()
        every { useCase.execute(any()) } throws NotFoundException("No listings data for the given properties: ${input.propertiesIds}")

        val handler = GetPropertiesListingsHandler(BaseUseCaseExecutor, useCase)
        // Then
        val error = assertThrows<RestException> { handler.invoke(input) }
        kotlin.test.assertNotNull(error)
        kotlin.test.assertEquals(error.message, "404 | NOT_FOUND - No listings data for the given properties: ${input.propertiesIds}")
        kotlin.test.assertEquals(error.statusCode, 404)
        kotlin.test.assertEquals(error.httpStatusCode, 404)
        kotlin.test.assertEquals(error.errorCode, "NOT_FOUND")
    }
}
