package adapters.repositories

import application.utils.base.BaseApplicationTest
import com.keyway.core.entities.GeoPoint
import com.keyway.core.entities.MultifamilyProperty
import com.keyway.core.ports.repositories.MultifamilyPropertyRepository
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertNull

class MultifamilyPropertyPostgresRepositoryTest : BaseApplicationTest() {
    private val repository: MultifamilyPropertyRepository by inject()

    @Test
    fun `should save and retrieve a multifamily property`() {
        // Given
        val property = createTestProperty()

        // When
        repository.saveOrUpdate(property)
        val retrievedProperty = repository.getByPropertyId(property.id)

        // Then
        assertEquals(property.id, retrievedProperty.id)
        assertEquals(property.address, retrievedProperty.address)
        assertEquals(property.city, retrievedProperty.city)
        assertEquals(property.county, retrievedProperty.county)
        assertEquals(property.zipCode, retrievedProperty.zipCode)
        assertEquals(property.state, retrievedProperty.state)
        assertEquals(property.location.latitude, retrievedProperty.location.latitude)
        assertEquals(property.location.longitude, retrievedProperty.location.longitude)
        assertEquals(property.squareFootage, retrievedProperty.squareFootage)
        assertEquals(property.squareFootagePerUnit, retrievedProperty.squareFootagePerUnit)
        assertEquals(property.sourceType, retrievedProperty.sourceType)
        assertEquals(property.tractCode, retrievedProperty.tractCode)
        assertEquals(property.constructionYear, retrievedProperty.constructionYear)
        assertEquals(property.renovationYear, retrievedProperty.renovationYear)
        assertEquals(property.unitQuantity, retrievedProperty.unitQuantity)
        assertEquals(property.occupancyPercentage, retrievedProperty.occupancyPercentage)
        assertEquals(property.propertyAmenities, retrievedProperty.propertyAmenities)
        assertEquals(property.unitsAmenities, retrievedProperty.unitsAmenities)
        assertEquals(property.isActive, retrievedProperty.isActive)
        assertEquals(property.qualityOverallScore, retrievedProperty.qualityOverallScore)
        assertEquals(property.stories, retrievedProperty.stories)
        assertEquals(property.propertyStyle, retrievedProperty.propertyStyle)
        assertEquals(property.housingSegment, retrievedProperty.housingSegment)
        assertEquals(property.hasAffordableUnits, retrievedProperty.hasAffordableUnits)
    }

    @Test
    fun `should update existing property on conflict`() {
        // Given
        val originalProperty = createTestProperty()
        repository.saveOrUpdate(originalProperty)

        // When - Update the property with new values
        val updatedProperty =
            originalProperty.copy(
                address = "456 Updated Street",
                city = "Updated City",
                unitQuantity = 200,
                isActive = false,
                qualityOverallScore = BigDecimal("8.5"),
                stories = 10,
                propertyStyle = "Updated Style",
                hasAffordableUnits = true,
            )
        repository.saveOrUpdate(updatedProperty)
        val retrievedProperty = repository.getByPropertyId(originalProperty.id)

        // Then
        assertEquals(updatedProperty.id, retrievedProperty.id)
        assertEquals(updatedProperty.address, retrievedProperty.address)
        assertEquals(updatedProperty.city, retrievedProperty.city)
        assertEquals(updatedProperty.unitQuantity, retrievedProperty.unitQuantity)
        assertEquals(updatedProperty.isActive, retrievedProperty.isActive)
        assertEquals(updatedProperty.qualityOverallScore, retrievedProperty.qualityOverallScore)
        assertEquals(updatedProperty.stories, retrievedProperty.stories)
        assertEquals(updatedProperty.propertyStyle, retrievedProperty.propertyStyle)
        assertEquals(updatedProperty.hasAffordableUnits, retrievedProperty.hasAffordableUnits)
    }

    @Test
    fun `should handle null values correctly`() {
        // Given
        val propertyWithNulls =
            createTestProperty().copy(
                county = null,
                squareFootage = null,
                squareFootagePerUnit = null,
                sourceType = null,
                tractCode = null,
                constructionYear = null,
                renovationYear = null,
                occupancyPercentage = null,
                qualityOverallScore = null,
                stories = null,
                propertyStyle = null,
                hasAffordableUnits = null,
            )

        // When
        repository.saveOrUpdate(propertyWithNulls)
        val retrievedProperty = repository.getByPropertyId(propertyWithNulls.id)

        // Then
        assertEquals(propertyWithNulls.id, retrievedProperty.id)
        assertNull(retrievedProperty.county)
        assertNull(retrievedProperty.squareFootage)
        assertNull(retrievedProperty.squareFootagePerUnit)
        assertNull(retrievedProperty.sourceType)
        assertNull(retrievedProperty.tractCode)
        assertNull(retrievedProperty.constructionYear)
        assertNull(retrievedProperty.renovationYear)
        assertNull(retrievedProperty.occupancyPercentage)
        assertNull(retrievedProperty.qualityOverallScore)
        assertNull(retrievedProperty.stories)
        assertNull(retrievedProperty.propertyStyle)
        assertNull(retrievedProperty.hasAffordableUnits)
    }

    @Test
    fun `should handle empty amenities sets`() {
        // Given
        val propertyWithEmptyAmenities =
            createTestProperty().copy(
                propertyAmenities = emptySet(),
                unitsAmenities = emptySet(),
                housingSegment = emptySet(),
            )

        // When
        repository.saveOrUpdate(propertyWithEmptyAmenities)
        val retrievedProperty = repository.getByPropertyId(propertyWithEmptyAmenities.id)

        // Then
        assertEquals(propertyWithEmptyAmenities.id, retrievedProperty.id)
        assertTrue(retrievedProperty.propertyAmenities.isEmpty())
        assertTrue(retrievedProperty.unitsAmenities.isEmpty())
        assertTrue(retrievedProperty.housingSegment.isEmpty())
    }

    @Test
    fun `should handle large amenities sets`() {
        // Given
        val largePropertyAmenities =
            setOf(
                "Pool",
                "Gym",
                "Parking",
                "Laundry",
                "Pet Friendly",
                "Balcony",
                "Air Conditioning",
                "Dishwasher",
                "Elevator",
                "Garden",
            )
        val largeUnitsAmenities =
            setOf(
                "Hardwood Floors",
                "Stainless Steel Appliances",
                "Walk-in Closet",
                "In-unit Washer/Dryer",
                "Granite Countertops",
                "Fireplace",
            )
        val largeHousingSegment = setOf("Luxury", "Mid-Market", "Affordable")

        val propertyWithLargeAmenities =
            createTestProperty().copy(
                propertyAmenities = largePropertyAmenities,
                unitsAmenities = largeUnitsAmenities,
                housingSegment = largeHousingSegment,
            )

        // When
        repository.saveOrUpdate(propertyWithLargeAmenities)
        val retrievedProperty = repository.getByPropertyId(propertyWithLargeAmenities.id)

        // Then
        assertEquals(propertyWithLargeAmenities.id, retrievedProperty.id)
        assertEquals(largePropertyAmenities, retrievedProperty.propertyAmenities)
        assertEquals(largeUnitsAmenities, retrievedProperty.unitsAmenities)
        assertEquals(largeHousingSegment, retrievedProperty.housingSegment)
    }

    private fun createTestProperty(): MultifamilyProperty =
        MultifamilyProperty(
            id = "TEST-${UUID.randomUUID()}",
            address = "123 Test Street",
            city = "Test City",
            county = "Test County",
            zipCode = 12345L,
            state = "TX",
            location =
                GeoPoint(
                    latitude = BigDecimal("32.7767"),
                    longitude = BigDecimal("-96.7970"),
                ),
            geolocation = "POINT(-96.7970 32.7767)",
            squareFootage = BigDecimal("50000"),
            squareFootagePerUnit = BigDecimal("1000"),
            sourceType = "ONE",
            tractCode = 48113001001L,
            constructionYear = 2020,
            renovationYear = 2022,
            unitQuantity = 50,
            occupancyPercentage = BigDecimal("95.5"),
            propertyAmenities = setOf("Pool", "Gym", "Parking"),
            unitsAmenities = setOf("Balcony", "Air Conditioning"),
            isActive = true,
            qualityOverallScore = BigDecimal("7.5"),
            lastSeen = LocalDate.now(),
            stories = 5,
            propertyStyle = "Modern",
            landSizeSqft = BigDecimal("75000"),
            housingSegment = setOf("Luxury", "Mid-Market"),
            hasAffordableUnits = false,
        )
}
